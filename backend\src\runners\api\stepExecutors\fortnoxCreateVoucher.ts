import axios from 'axios';
import { FortnoxCreateVoucherStep } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';

/**
 * Executor context for Fortnox steps
 */
export interface FortnoxExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
}

/**
 * Fortnox API account interface
 */
interface FortnoxAccount {
  Number: string;
  Description: string;
  Active: boolean;
  BalanceBroughtForward?: number;
  BalanceCarriedForward?: number;
  SRU?: number;
  Year?: number;
}

/**
 * Fortnox API chart of accounts response
 */
interface FortnoxAccountsResponse {
  Accounts: FortnoxAccount[];
}

/**
 * Fortnox voucher row interface
 */
interface FortnoxVoucherRow {
  Account: string;
  Debit?: number;
  Credit?: number;
  Description?: string;
}

/**
 * Fortnox voucher interface
 */
interface FortnoxVoucher {
  Description?: string;
  TransactionDate?: string;
  VoucherSeries?: string;
  VoucherRows: FortnoxVoucherRow[];
}

/**
 * Execute Fortnox Create Voucher step
 */
export async function executeFortnoxCreateVoucher(
  step: FortnoxCreateVoucherStep,
  context: FortnoxExecutorContext
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing Fortnox Create Voucher: ${step.name}`,
      stepId: step.id
    });

    // Get Fortnox token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for Fortnox API calls');
    }

    const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
    const fortnoxToken = fortnoxTokens.find(token => token.provider === 'Fortnox' && token.apiToken);

    if (!fortnoxToken || !fortnoxToken.apiToken) {
      throw new Error('No valid Fortnox token found for customer');
    }

    onLog({
      level: 'info',
      message: 'Found Fortnox token, fetching chart of accounts...',
      stepId: step.id
    });

    // Fetch chart of accounts from Fortnox
    const accountsResponse = await axios.get<FortnoxAccountsResponse>(
      'https://api.fortnox.se/3/accounts',
      {
        headers: {
          'Authorization': `Bearer ${fortnoxToken.apiToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const accounts = accountsResponse.data.Accounts;
    onLog({
      level: 'info',
      message: `Fetched ${accounts.length} accounts from Fortnox`,
      stepId: step.id
    });

    // Store accounts in variables for potential use in other steps
    variables['var-fortnox-accounts'] = accounts;

    // Validate account mappings and build voucher rows
    const voucherRows: FortnoxVoucherRow[] = [];
    let totalDebit = 0;
    let totalCredit = 0;

    for (const mapping of step.accountMappings) {
      // Find the account in the chart of accounts
      const account = accounts.find(acc => acc.Number === mapping.accountNumber);
      if (!account) {
        throw new Error(`Account ${mapping.accountNumber} not found in chart of accounts`);
      }

      // Get the amount from variables
      const amountVariable = variables[mapping.variableName];
      if (amountVariable === undefined || amountVariable === null) {
        throw new Error(`Variable ${mapping.variableName} not found or is null`);
      }

      const amount = parseFloat(amountVariable.toString());
      if (isNaN(amount) || amount <= 0) {
        throw new Error(`Variable ${mapping.variableName} must contain a positive number, got: ${amountVariable}`);
      }

      // Create voucher row
      const voucherRow: FortnoxVoucherRow = {
        Account: mapping.accountNumber,
        Description: interpolateVariables(step.description || '', variables)
      };

      if (mapping.debitCredit === 'debit') {
        voucherRow.Debit = amount;
        totalDebit += amount;
      } else {
        voucherRow.Credit = amount;
        totalCredit += amount;
      }

      voucherRows.push(voucherRow);

      onLog({
        level: 'info',
        message: `Added ${mapping.debitCredit} entry: Account ${mapping.accountNumber} (${account.Description}) = ${amount}`,
        stepId: step.id
      });
    }

    // Validate that debits equal credits
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      throw new Error(`Voucher is not balanced: Total debit (${totalDebit}) != Total credit (${totalCredit})`);
    }

    // Create voucher
    const voucher: FortnoxVoucher = {
      Description: interpolateVariables(step.description || 'RPA Generated Voucher', variables),
      TransactionDate: step.transactionDate || new Date().toISOString().split('T')[0],
      VoucherSeries: step.voucherSeries || 'A',
      VoucherRows: voucherRows
    };

    onLog({
      level: 'info',
      message: `Creating voucher with ${voucherRows.length} rows (Total: ${totalDebit})`,
      stepId: step.id
    });

    // Send voucher to Fortnox
    const voucherResponse = await axios.post(
      'https://api.fortnox.se/3/vouchers',
      { Voucher: voucher },
      {
        headers: {
          'Authorization': `Bearer ${fortnoxToken.apiToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const createdVoucher = voucherResponse.data.Voucher;
    
    // Store voucher information in variables
    variables['var-fortnox-voucher-number'] = createdVoucher.VoucherNumber;
    variables['var-fortnox-voucher-series'] = createdVoucher.VoucherSeries;
    variables['var-fortnox-voucher-id'] = createdVoucher.VoucherNumber;
    variables['var-fortnox-voucher-response'] = createdVoucher;

    onLog({
      level: 'info',
      message: `Voucher created successfully: ${createdVoucher.VoucherSeries}${createdVoucher.VoucherNumber}`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        'var-fortnox-accounts': accounts,
        'var-fortnox-voucher-number': createdVoucher.VoucherNumber,
        'var-fortnox-voucher-series': createdVoucher.VoucherSeries,
        'var-fortnox-voucher-id': createdVoucher.VoucherNumber,
        'var-fortnox-voucher-response': createdVoucher
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Error creating Fortnox voucher: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
