import { RpaStep, FlowSettings } from '@rpa-project/shared';
import { <PERSON>Runner, RunnerContext, StepExecutionResult } from '../base';
import { STEP_RUNNER_MAPPING } from '../registry/stepTypes';
import { executeFortnoxCreateVoucher } from './stepExecutors';

/**
 * APIRunner for handling API-based steps including Fortnox integration
 * Supports various API integrations for RPA workflows
 */
export class APIRunner extends BaseRunner {

  async initialize(settings: FlowSettings, variables: Record<string, any> = {}): Promise<void> {
    // API runner doesn't need special initialization
    this.log('info', 'APIRunner initialized');
  }

  getSupportedStepTypes(): string[] {
    return Object.keys(STEP_RUNNER_MAPPING).filter(
      stepType => STEP_RUNNER_MAPPING[stepType as keyof typeof STEP_RUNNER_MAPPING] === 'api'
    );
  }

  async executeStep(step: RpaStep, context: RunnerContext): Promise<StepExecutionResult> {
    const { variables, onLog } = context;

    try {
      onLog({
        level: 'info',
        message: `Executing API step: ${step.type}`,
        stepId: step.id
      });

      // Create executor context
      const executorContext = {
        variables: context.variables,
        onLog,
        interpolateVariables: this.interpolateVariables.bind(this),
        customerId: context.customerId
      };

      // Route to appropriate step executor
      switch (step.type) {
        case 'fortnoxCreateVoucher':
          return await executeFortnoxCreateVoucher(step as any, executorContext);

        default:
          return {
            success: false,
            error: `Unsupported API step type: ${step.type}`
          };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onLog({
        level: 'error',
        message: `Error executing API step ${step.type}: ${errorMessage}`,
        stepId: step.id
      });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async cleanup(): Promise<void> {
    // API runner doesn't need cleanup
  }
}
