import { RpaStep, ValidationResult, ValidationError } from '../../types';
import type { FortnoxCreateVoucherStep, FortnoxAccountMapping } from '../../types/steps/api';

/**
 * Validerar Fortnox Create Voucher steg
 */
export function validateFortnoxCreateVoucherStep(step: FortnoxCreateVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxCreateVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxCreateVoucher"',
      code: 'INVALID_TYPE'
    });
  }

  // Validera kontomappningar
  if (!step.accountMappings || step.accountMappings.length === 0) {
    errors.push({
      field: 'accountMappings',
      message: 'Minst en kontomappning krävs',
      code: 'REQUIRED'
    });
  } else {
    step.accountMappings.forEach((mapping, index) => {
      validateAccountMapping(mapping, index, errors);
    });

    // Kontrollera att debet och kredit balanserar (om alla mappningar har numeriska värden)
    const debitMappings = step.accountMappings.filter(m => m.debitCredit === 'debit');
    const creditMappings = step.accountMappings.filter(m => m.debitCredit === 'credit');

    if (debitMappings.length === 0) {
      errors.push({
        field: 'accountMappings',
        message: 'Minst en debet-mappning krävs',
        code: 'REQUIRED'
      });
    }

    if (creditMappings.length === 0) {
      errors.push({
        field: 'accountMappings',
        message: 'Minst en kredit-mappning krävs',
        code: 'REQUIRED'
      });
    }
  }

  // Validera verifikationsserie
  if (step.voucherSeries && !['A', 'B', 'C', 'D'].includes(step.voucherSeries)) {
    errors.push({
      field: 'voucherSeries',
      message: 'Verifikationsserie måste vara A, B, C eller D',
      code: 'INVALID_VALUE'
    });
  }

  // Validera transaktionsdatum format (om angivet)
  if (step.transactionDate && !isValidDateFormat(step.transactionDate)) {
    errors.push({
      field: 'transactionDate',
      message: 'Transaktionsdatum måste vara i format YYYY-MM-DD eller en variabel',
      code: 'INVALID_FORMAT'
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validerar en kontomappning
 */
function validateAccountMapping(mapping: FortnoxAccountMapping, index: number, errors: ValidationError[]): void {
  const fieldPrefix = `accountMappings[${index}]`;

  if (!mapping.accountNumber?.trim()) {
    errors.push({
      field: `${fieldPrefix}.accountNumber`,
      message: `Kontonummer är obligatoriskt för mappning ${index + 1}`,
      code: 'REQUIRED'
    });
  }

  if (!mapping.variableName?.trim()) {
    errors.push({
      field: `${fieldPrefix}.variableName`,
      message: `Variabelnamn är obligatoriskt för mappning ${index + 1}`,
      code: 'REQUIRED'
    });
  }

  if (!mapping.debitCredit || !['debit', 'credit'].includes(mapping.debitCredit)) {
    errors.push({
      field: `${fieldPrefix}.debitCredit`,
      message: `Debet/Kredit måste vara 'debit' eller 'credit' för mappning ${index + 1}`,
      code: 'INVALID_VALUE'
    });
  }
}

/**
 * Kontrollerar om datum är i giltigt format (YYYY-MM-DD eller variabel)
 */
function isValidDateFormat(date: string): boolean {
  // Tillåt variabler (börjar med ${)
  if (date.startsWith('${') && date.endsWith('}')) {
    return true;
  }

  // Kontrollera YYYY-MM-DD format
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(date)) {
    return false;
  }

  // Kontrollera att det är ett giltigt datum
  const parsedDate = new Date(date);
  return parsedDate instanceof Date && !isNaN(parsedDate.getTime());
}

/**
 * Skapar ett nytt Fortnox Create Voucher steg med standardvärden
 */
export function createFortnoxCreateVoucherStep(): FortnoxCreateVoucherStep {
  return {
    id: '',
    type: 'fortnoxCreateVoucher',
    description: 'Skapa verifikation i Fortnox',
    voucherSeries: 'A',
    accountMappings: []
  };
}

// Huvudvalidator för API-steg
export function validateApiStep(step: RpaStep): ValidationResult {
  switch ((step as any).type) {
    case 'fortnoxCreateVoucher':
      return validateFortnoxCreateVoucherStep(step as unknown as FortnoxCreateVoucherStep);
    default:
      return {
        valid: false,
        errors: [{
          field: 'type',
          message: `Okänd API steg-typ: ${step.type}`,
          code: 'INVALID_TYPE'
        }]
      };
  }
}

export function createApiStepFromType(stepType: string): any {
  switch (stepType) {
    case 'fortnoxCreateVoucher':
      return createFortnoxCreateVoucherStep();
    default:
      throw new Error(`Okänd API steg-typ: ${stepType}`);
  }
}
